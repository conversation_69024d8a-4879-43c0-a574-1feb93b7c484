/**
 * نظام إدارة المخاطر المتقدم
 * Advanced Risk Management System
 */

class RiskManagement {
    constructor(options = {}) {
        this.maxRiskPerTrade = options.maxRiskPerTrade || 0.02; // 2% من رأس المال
        this.maxDailyLoss = options.maxDailyLoss || 0.05; // 5% خسارة يومية قصوى
        this.maxConsecutiveLosses = options.maxConsecutiveLosses || 3;
        this.minWinRate = options.minWinRate || 0.6; // 60% نسبة نجاح دنيا
        this.maxDrawdown = options.maxDrawdown || 0.10; // 10% انخفاض أقصى
        
        // إحصائيات التداول
        this.stats = {
            totalTrades: 0,
            winningTrades: 0,
            losingTrades: 0,
            consecutiveLosses: 0,
            dailyPnL: 0,
            totalPnL: 0,
            maxDrawdownReached: 0,
            lastResetDate: new Date().toDateString()
        };
        
        // سجل الصفقات
        this.tradeHistory = [];
    }

    /**
     * حساب حجم الصفقة المناسب
     */
    calculatePositionSize(accountBalance, riskAmount = null) {
        if (riskAmount) {
            return Math.min(riskAmount, accountBalance * this.maxRiskPerTrade);
        }
        
        return accountBalance * this.maxRiskPerTrade;
    }

    /**
     * التحقق من إمكانية فتح صفقة جديدة
     */
    canOpenTrade(accountBalance, proposedAmount) {
        const checks = {
            positionSize: this.checkPositionSize(accountBalance, proposedAmount),
            dailyLoss: this.checkDailyLoss(accountBalance),
            consecutiveLosses: this.checkConsecutiveLosses(),
            winRate: this.checkWinRate(),
            drawdown: this.checkDrawdown(accountBalance)
        };

        const canTrade = Object.values(checks).every(check => check.allowed);
        
        return {
            allowed: canTrade,
            checks: checks,
            recommendedAmount: this.calculatePositionSize(accountBalance)
        };
    }

    /**
     * فحص حجم الصفقة
     */
    checkPositionSize(accountBalance, amount) {
        const maxAllowed = accountBalance * this.maxRiskPerTrade;
        const allowed = amount <= maxAllowed;
        
        return {
            allowed: allowed,
            reason: allowed ? 'Position size within limits' : `Amount ${amount} exceeds max risk ${maxAllowed}`,
            maxAllowed: maxAllowed
        };
    }

    /**
     * فحص الخسارة اليومية
     */
    checkDailyLoss(accountBalance) {
        this.resetDailyStatsIfNeeded();
        
        const maxDailyLossAmount = accountBalance * this.maxDailyLoss;
        const allowed = Math.abs(this.stats.dailyPnL) < maxDailyLossAmount;
        
        return {
            allowed: allowed,
            reason: allowed ? 'Daily loss within limits' : `Daily loss ${this.stats.dailyPnL} exceeds limit ${maxDailyLossAmount}`,
            currentDailyPnL: this.stats.dailyPnL,
            maxDailyLoss: maxDailyLossAmount
        };
    }

    /**
     * فحص الخسائر المتتالية
     */
    checkConsecutiveLosses() {
        const allowed = this.stats.consecutiveLosses < this.maxConsecutiveLosses;
        
        return {
            allowed: allowed,
            reason: allowed ? 'Consecutive losses within limits' : `${this.stats.consecutiveLosses} consecutive losses reached limit`,
            consecutiveLosses: this.stats.consecutiveLosses,
            maxAllowed: this.maxConsecutiveLosses
        };
    }

    /**
     * فحص نسبة النجاح
     */
    checkWinRate() {
        if (this.stats.totalTrades < 10) {
            return { allowed: true, reason: 'Insufficient trades for win rate analysis' };
        }
        
        const currentWinRate = this.stats.winningTrades / this.stats.totalTrades;
        const allowed = currentWinRate >= this.minWinRate;
        
        return {
            allowed: allowed,
            reason: allowed ? 'Win rate acceptable' : `Win rate ${(currentWinRate * 100).toFixed(1)}% below minimum ${(this.minWinRate * 100)}%`,
            currentWinRate: currentWinRate,
            minWinRate: this.minWinRate
        };
    }

    /**
     * فحص الانخفاض الأقصى
     */
    checkDrawdown(accountBalance) {
        const drawdownPercent = this.stats.maxDrawdownReached / accountBalance;
        const allowed = drawdownPercent < this.maxDrawdown;
        
        return {
            allowed: allowed,
            reason: allowed ? 'Drawdown within limits' : `Drawdown ${(drawdownPercent * 100).toFixed(1)}% exceeds limit ${(this.maxDrawdown * 100)}%`,
            currentDrawdown: drawdownPercent,
            maxDrawdown: this.maxDrawdown
        };
    }

    /**
     * تسجيل نتيجة صفقة
     */
    recordTrade(result) {
        const trade = {
            timestamp: new Date(),
            amount: result.amount,
            profit: result.profit,
            isWin: result.profit > 0,
            instrumentId: result.instrumentId,
            direction: result.direction
        };

        this.tradeHistory.push(trade);
        this.updateStats(trade);
        
        return this.generateTradeReport(trade);
    }

    /**
     * تحديث الإحصائيات
     */
    updateStats(trade) {
        this.stats.totalTrades++;
        this.stats.dailyPnL += trade.profit;
        this.stats.totalPnL += trade.profit;

        if (trade.isWin) {
            this.stats.winningTrades++;
            this.stats.consecutiveLosses = 0;
        } else {
            this.stats.losingTrades++;
            this.stats.consecutiveLosses++;
        }

        // تحديث الانخفاض الأقصى
        if (trade.profit < 0) {
            this.stats.maxDrawdownReached = Math.max(
                this.stats.maxDrawdownReached,
                Math.abs(trade.profit)
            );
        }
    }

    /**
     * إعادة تعيين الإحصائيات اليومية
     */
    resetDailyStatsIfNeeded() {
        const today = new Date().toDateString();
        if (this.stats.lastResetDate !== today) {
            this.stats.dailyPnL = 0;
            this.stats.lastResetDate = today;
        }
    }

    /**
     * إنتاج تقرير الصفقة
     */
    generateTradeReport(trade) {
        const winRate = this.stats.totalTrades > 0 ? 
            (this.stats.winningTrades / this.stats.totalTrades * 100).toFixed(1) : 0;

        return {
            trade: trade,
            stats: {
                totalTrades: this.stats.totalTrades,
                winRate: `${winRate}%`,
                dailyPnL: this.stats.dailyPnL,
                totalPnL: this.stats.totalPnL,
                consecutiveLosses: this.stats.consecutiveLosses
            },
            recommendations: this.generateRecommendations()
        };
    }

    /**
     * إنتاج التوصيات
     */
    generateRecommendations() {
        const recommendations = [];

        if (this.stats.consecutiveLosses >= 2) {
            recommendations.push('Consider reducing position size due to consecutive losses');
        }

        if (this.stats.totalTrades > 10) {
            const winRate = this.stats.winningTrades / this.stats.totalTrades;
            if (winRate < this.minWinRate) {
                recommendations.push('Win rate below target - review strategy');
            }
        }

        if (this.stats.dailyPnL < 0) {
            recommendations.push('Daily P&L negative - consider taking a break');
        }

        return recommendations;
    }

    /**
     * الحصول على تقرير شامل
     */
    getFullReport() {
        this.resetDailyStatsIfNeeded();
        
        const winRate = this.stats.totalTrades > 0 ? 
            this.stats.winningTrades / this.stats.totalTrades : 0;

        return {
            riskSettings: {
                maxRiskPerTrade: `${(this.maxRiskPerTrade * 100)}%`,
                maxDailyLoss: `${(this.maxDailyLoss * 100)}%`,
                maxConsecutiveLosses: this.maxConsecutiveLosses,
                minWinRate: `${(this.minWinRate * 100)}%`,
                maxDrawdown: `${(this.maxDrawdown * 100)}%`
            },
            currentStats: {
                totalTrades: this.stats.totalTrades,
                winningTrades: this.stats.winningTrades,
                losingTrades: this.stats.losingTrades,
                winRate: `${(winRate * 100).toFixed(1)}%`,
                consecutiveLosses: this.stats.consecutiveLosses,
                dailyPnL: this.stats.dailyPnL,
                totalPnL: this.stats.totalPnL,
                maxDrawdownReached: this.stats.maxDrawdownReached
            },
            recentTrades: this.tradeHistory.slice(-10),
            recommendations: this.generateRecommendations()
        };
    }

    /**
     * إعادة تعيين الإحصائيات
     */
    resetStats() {
        this.stats = {
            totalTrades: 0,
            winningTrades: 0,
            losingTrades: 0,
            consecutiveLosses: 0,
            dailyPnL: 0,
            totalPnL: 0,
            maxDrawdownReached: 0,
            lastResetDate: new Date().toDateString()
        };
        this.tradeHistory = [];
    }
}

module.exports = RiskManagement;
