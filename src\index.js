
const WebSocket = require(\'ws\');
const EventEmitter = require(\'events\');

class QuotexAPI extends EventEmitter {
    constructor(sessionId, wsUrl = 'wss://ws.qxbroker.com/socket.io/?EIO=3&transport=websocket') {
        super();
        this.sessionId = sessionId;
        this.wsUrl = wsUrl;
        this.ws = null;
        this.isConnected = false;
        this.isAuthenticated = false;
        this.messageHandlers = {};
        this.reconnectInterval = 5000; // 5 seconds
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 10;
        this.heartbeatInterval = null;
        this.requestId = 1;
        this.pendingRequests = new Map();

        // Callbacks for specific data types
        this.onInstrumentsList = null;
        this.onMarketData = null;
        this.onHistoricalCandles = null;
        this.onAccountBalance = null;
        this.onProfitPercentage = null;
        this.onTradeHistory = null;
        this.onTradeResult = null;
        this.onLiveCandles = null;
    }

    async connect() {
        return new Promise((resolve, reject) => {
            try {
                console.log('Attempting to connect to:', this.wsUrl);
                this.ws = new WebSocket(this.wsUrl);

                this.ws.onopen = () => {
                    console.log('✅ Connected to WebSocket successfully');
                    this.isConnected = true;
                    this.reconnectAttempts = 0;
                    this.startHeartbeat();

                    // إرسال رسالة المصادقة
                    try {
                        this.send(this.sessionId);
                        console.log('🔐 Authentication message sent');
                    } catch (error) {
                        console.error('❌ Failed to send authentication:', error);
                        reject(error);
                        return;
                    }

                    resolve();
                };

            this.ws.onmessage = (event) => {
                try {
                    this.handleMessage(event.data);
                } catch (error) {
                    console.error('❌ Error handling message:', error, event.data);
                    this.emit('error', error);
                }
            };

            this.ws.onclose = (event) => {
                console.log(`🔌 Disconnected from WebSocket. Code: ${event.code}, Reason: ${event.reason}`);
                this.isConnected = false;
                this.isAuthenticated = false;
                this.stopHeartbeat();
                this.emit('disconnected', event.code, event.reason);

                if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.reconnectAttempts++;
                    console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
                    setTimeout(() => this.connect().catch(console.error), this.reconnectInterval);
                } else if (this.reconnectAttempts >= this.maxReconnectAttempts) {
                    console.error('❌ Max reconnect attempts reached. Please check your connection or session ID.');
                    const error = new Error('Max reconnect attempts reached');
                    this.emit('error', error);
                    reject(error);
                }
            };

            this.ws.onerror = (error) => {
                console.error(`❌ WebSocket Error: ${error.message}`);
                this.isConnected = false;
                this.isAuthenticated = false;
                this.stopHeartbeat();
                this.emit('error', error);
                reject(error);
            };

        } catch (error) {
            console.error('❌ Failed to create WebSocket connection:', error);
            reject(error);
        }
        });
    }

    // دالة منفصلة لمعالجة الرسائل
    handleMessage(data) {
        console.log('📨 Received message:', data);

        // معالجة رسائل Socket.IO
        if (data === '0') {
            console.log('🔗 Initial WebSocket connection established');
            return;
        }

        if (data === '40') {
            console.log('✅ Authentication successful');
            this.isAuthenticated = true;
            this.emit('authenticated');
            return;
        }

        if (data === '3') {
            // Heartbeat response
            return;
        }

        // معالجة رسائل البيانات
        if (data.startsWith('42')) {
            try {
                const message = JSON.parse(data.substring(2));
                const command = message[0];
                const payload = message[1];

                console.log(`📊 Processing command: ${command}`);

                // Handle specific data types
                switch (command) {
                    case 'instruments/list':
                        if (this.onInstrumentsList) this.onInstrumentsList(payload);
                        this.emit('instrumentsList', payload);
                        break;
                    case 'timeSync':
                    case 'market-data':
                        if (this.onMarketData) this.onMarketData(payload);
                        this.emit('marketData', payload);
                        break;
                    case 'candles/history':
                        if (this.onHistoricalCandles) this.onHistoricalCandles(payload);
                        this.emit('historicalCandles', payload);
                        break;
                    case 'candles/live':
                        if (this.onLiveCandles) this.onLiveCandles(payload);
                        this.emit('liveCandles', payload);
                        break;
                    case 'balance/get':
                        if (this.onAccountBalance) this.onAccountBalance(payload);
                        this.emit('accountBalance', payload);
                        break;
                    case 'profile/profit-percentage':
                        if (this.onProfitPercentage) this.onProfitPercentage(payload);
                        this.emit('profitPercentage', payload);
                        break;
                    case 'trading/history':
                        if (this.onTradeHistory) this.onTradeHistory(payload);
                        this.emit('tradeHistory', payload);
                        break;
                    case 'trading/open-trade-result':
                        if (this.onTradeResult) this.onTradeResult(payload);
                        this.emit('tradeResult', payload);
                        break;
                    default:
                        if (this.messageHandlers[command]) {
                            this.messageHandlers[command](payload);
                        } else {
                            console.log(`⚠️ Unhandled API message: ${command}`, payload);
                            this.emit('unhandledMessage', { command, payload });
                        }
                }
            } catch (error) {
                console.error('❌ Error parsing data message:', error, data);
                this.emit('parseError', { error, data });
            }
        } else {
            console.log('⚠️ Unknown message format:', data);
            this.emit('unknownMessage', data);
        }
    }

    emit(event, ...args) {
        if (this.messageHandlers[event]) {
            this.messageHandlers[event](...args);
        }
        super.emit(event, ...args);
    }

    on(event, handler) {
        this.messageHandlers[event] = handler;
        super.on(event, handler);
    }

    // إضافة دالة send المفقودة
    send(message) {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            throw new Error('WebSocket is not connected');
        }

        try {
            this.ws.send(message);
            console.log('Message sent:', message);
        } catch (error) {
            console.error('Error sending message:', error);
            this.emit('error', error);
            throw error;
        }
    }

    // دالة للتحقق من حالة الاتصال
    isConnectionReady() {
        return this.ws && this.ws.readyState === WebSocket.OPEN && this.isConnected;
    }

    // دالة لإرسال heartbeat
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnectionReady()) {
                try {
                    this.send('2'); // Socket.IO heartbeat
                } catch (error) {
                    console.error('Heartbeat failed:', error);
                }
            }
        }, 25000); // كل 25 ثانية
    }

    // إيقاف heartbeat
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    // Data Retrieval Methods

    async getInstrumentsList() {
        return new Promise(resolve => {
            this.onInstrumentsList = (data) => {
                resolve(data);
                this.onInstrumentsList = null; // Clear handler after use
            };
            const message = '42[\"instruments/list\",{\"_placeholder\":true,\"num\":0}]';
            this.send(message);
        });
    }

    async getMarketData(instrumentId) {
        // This is a subscription, so it will continuously send data
        // The user should handle the data via the onMarketData callback
        const message = `42[\"timeSync\",{\"instrument_id\":${instrumentId}}]`;
        this.send(message);
    }

    async getHistoricalCandles(instrumentId, timeframe, count) {
        return new Promise(resolve => {
            this.onHistoricalCandles = (data) => {
                resolve(data);
                this.onHistoricalCandles = null; // Clear handler after use
            };
            const message = `42[\"candles/history\",{\"instrument_id\":${instrumentId},\"timeframe\":${timeframe},\"count\":${count}}]`;
            this.send(message);
        });
    }

    async getAccountBalance() {
        return new Promise(resolve => {
            this.onAccountBalance = (data) => {
                resolve(data);
                this.onAccountBalance = null; // Clear handler after use
            };
            const message = '42[\"balance/get\",{}]';
            this.send(message);
        });
    }

    async getProfitPercentage(instrumentId) {
        return new Promise(resolve => {
            this.onProfitPercentage = (data) => {
                resolve(data);
                this.onProfitPercentage = null; // Clear handler after use
            };
            const message = `42[\"profile/profit-percentage\",{\"instrument_id\":${instrumentId}}]`;
            this.send(message);
        });
    }

    async getTradeHistory() {
        return new Promise(resolve => {
            this.onTradeHistory = (data) => {
                resolve(data);
                this.onTradeHistory = null; // Clear handler after use
            };
            const message = '42[\"trading/history\",{}]';
            this.send(message);
        });
    }

    // Trading Methods

    async placeTrade(instrumentId, amount, direction, duration) {
        return new Promise(resolve => {
            this.onTradeResult = (data) => {
                resolve(data);
                this.onTradeResult = null; // Clear handler after use
            };
            const tradeMessage = `42[\"trading/open-trade\",{\"instrument_id\":${instrumentId},\"amount\":${amount},\"direction\":\"${direction}\",\"duration\":${duration}}]`;
            this.send(tradeMessage);
        });
    }

    close() {
        if (this.ws) {
            this.ws.close(1000, 'Normal Closure');
        }
    }
}

module.exports = QuotexAPI;


