
const WebSocket = require(\'ws\');

class QuotexAPI {
    constructor(sessionId, wsUrl = 'wss://iqoption.com/echo/websocket') {
        this.sessionId = sessionId;
        this.wsUrl = wsUrl;
        this.ws = null;
        this.isConnected = false;
        this.messageHandlers = {};
        this.reconnectInterval = 5000; // 5 seconds
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 10;

        // Callbacks for specific data types
        this.onInstrumentsList = null;
        this.onMarketData = null;
        this.onHistoricalCandles = null;
        this.onAccountBalance = null;
        this.onProfitPercentage = null;
        this.onTradeHistory = null;
        this.onTradeResult = null;
    }

    async connect() {
        return new Promise((resolve, reject) => {
            this.ws = new WebSocket(this.wsUrl);

            this.ws.onopen = () => {
                console.log('Connected to WebSocket');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.ws.send(this.sessionId);
                resolve();
            };

            this.ws.onmessage = (event) => {
                if (event.data.startsWith('42')) {
                    try {
                        const message = JSON.parse(event.data.substring(2));
                        const command = message[0];
                        const payload = message[1];

                        // Handle specific data types
                        switch (command) {
                            case 'instruments/list':
                                if (this.onInstrumentsList) this.onInstrumentsList(payload);
                                break;
                            case 'timeSync': // Assuming timeSync provides real-time market data
                                if (this.onMarketData) this.onMarketData(payload);
                                break;
                            case 'candles/history':
                                if (this.onHistoricalCandles) this.onHistoricalCandles(payload);
                                break;
                            case 'balance/get':
                                if (this.onAccountBalance) this.onAccountBalance(payload);
                                break;
                            case 'profile/profit-percentage':
                                if (this.onProfitPercentage) this.onProfitPercentage(payload);
                                break;
                            case 'trading/history':
                                if (this.onTradeHistory) this.onTradeHistory(payload);
                                break;
                            case 'trading/open-trade-result':
                                if (this.onTradeResult) this.onTradeResult(payload);
                                break;
                            default:
                                if (this.messageHandlers[command]) {
                                    this.messageHandlers[command](payload);
                                } else {
                                    console.log(`Unhandled API message: ${command}`, payload);
                                }
                        }
                    } catch (e) {
                        console.error('Error parsing message:', e, event.data);
                    }
                } else if (event.data === '40') {
                    console.log('Authentication successful (received 40)');
                    this.emit('authenticated');
                } else if (event.data === '0') {
                    console.log('Initial WebSocket connection message (0):', event.data);
                }
            };

            this.ws.onclose = (event) => {
                console.log(`Disconnected from WebSocket. Code: ${event.code}, Reason: ${event.reason}`);
                this.isConnected = false;
                this.emit('disconnected', event.code, event.reason);
                if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) { // 1000 is normal closure
                    this.reconnectAttempts++;
                    console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
                    setTimeout(() => this.connect(), this.reconnectInterval);
                } else if (this.reconnectAttempts >= this.maxReconnectAttempts) {
                    console.error('Max reconnect attempts reached. Please check your connection or session ID.');
                    this.emit('error', new Error('Max reconnect attempts reached'));
                    reject(new Error('Max reconnect attempts reached'));
                }
            };

            this.ws.onerror = (error) => {
                console.error(`WebSocket Error: ${error.message}`);
                this.isConnected = false;
                this.emit('error', error);
                this.ws.close(); // Close to trigger onclose and potential reconnect
                reject(error);
            };
        });
    }

    emit(event, ...args) {
        if (this.messageHandlers[event]) {
            this.messageHandlers[event](...args);
        }
    }

    on(event, handler) {
        this.messageHandlers[event] = handler;
    }

    // Data Retrieval Methods

    async getInstrumentsList() {
        return new Promise(resolve => {
            this.onInstrumentsList = (data) => {
                resolve(data);
                this.onInstrumentsList = null; // Clear handler after use
            };
            const message = '42[\"instruments/list\",{\"_placeholder\":true,\"num\":0}]';
            this.send(message);
        });
    }

    async getMarketData(instrumentId) {
        // This is a subscription, so it will continuously send data
        // The user should handle the data via the onMarketData callback
        const message = `42[\"timeSync\",{\"instrument_id\":${instrumentId}}]`;
        this.send(message);
    }

    async getHistoricalCandles(instrumentId, timeframe, count) {
        return new Promise(resolve => {
            this.onHistoricalCandles = (data) => {
                resolve(data);
                this.onHistoricalCandles = null; // Clear handler after use
            };
            const message = `42[\"candles/history\",{\"instrument_id\":${instrumentId},\"timeframe\":${timeframe},\"count\":${count}}]`;
            this.send(message);
        });
    }

    async getAccountBalance() {
        return new Promise(resolve => {
            this.onAccountBalance = (data) => {
                resolve(data);
                this.onAccountBalance = null; // Clear handler after use
            };
            const message = '42[\"balance/get\",{}]';
            this.send(message);
        });
    }

    async getProfitPercentage(instrumentId) {
        return new Promise(resolve => {
            this.onProfitPercentage = (data) => {
                resolve(data);
                this.onProfitPercentage = null; // Clear handler after use
            };
            const message = `42[\"profile/profit-percentage\",{\"instrument_id\":${instrumentId}}]`;
            this.send(message);
        });
    }

    async getTradeHistory() {
        return new Promise(resolve => {
            this.onTradeHistory = (data) => {
                resolve(data);
                this.onTradeHistory = null; // Clear handler after use
            };
            const message = '42[\"trading/history\",{}]';
            this.send(message);
        });
    }

    // Trading Methods

    async placeTrade(instrumentId, amount, direction, duration) {
        return new Promise(resolve => {
            this.onTradeResult = (data) => {
                resolve(data);
                this.onTradeResult = null; // Clear handler after use
            };
            const tradeMessage = `42[\"trading/open-trade\",{\"instrument_id\":${instrumentId},\"amount\":${amount},\"direction\":\"${direction}\",\"duration\":${duration}}]`;
            this.send(tradeMessage);
        });
    }

    close() {
        if (this.ws) {
            this.ws.close(1000, 'Normal Closure');
        }
    }
}

module.exports = QuotexAPI;


