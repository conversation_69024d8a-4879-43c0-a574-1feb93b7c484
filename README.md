# Quotex WebSocket API (quotex-websocket-api)

مكتبة Node.js للتفاعل مع منصة Quotex للتداول الثنائي عبر WebSocket. تهدف هذه المكتبة إلى توفير واجهة برمجية (API) سهلة الاستخدام لجلب بيانات السوق، البيانات اللحظية للشموع، قائمة الأزواج، البيانات التاريخية، الرصيد، نسبة الربح لكل زوج، وسجل الصفقات المغلقة والمفتوحة، بالإضافة إلى إمكانية إجراء عمليات التداول.

تم تصميم هذه المكتبة لتكون مشابهة في وظائفها لمكتبة `BinaryOptionsTools-v2` ولكن مخصصة لمنصة Quotex.

## التثبيت

للتثبيت، انتقل إلى مجلد مشروعك وقم بتشغيل الأمر التالي:

```bash
npm install quotex-websocket-api
```

أو إذا كنت تعمل ضمن هذا المستودع:

```bash
npm install
```

## الاستخدام

### الاتصال والمصادقة

للاتصال بمنصة Quotex، ستحتاج إلى `SESSION_ID`. يمكنك الحصول على هذا المعرف من خلال تحليل حركة مرور WebSocket الخاصة بك عند تسجيل الدخول إلى منصة Quotex. يجب أن يكون `SESSION_ID` على الشكل التالي:

`42["authorization",{"session":"YOUR_SESSION_ID_HERE","isDemo":1,"tournamentId":0}]`

```javascript
const QuotexAPI = require(\'quotex-websocket-api\');

const SESSION_ID = \'42[\\"authorization\\",{\\"session\\":\\"YOUR_SESSION_ID_HERE\\",\\"isDemo\\":1,\\"tournamentId\\":0}]\'; // استبدل بمعرف الجلسة الخاص بك

async function main() {
    const quotex = new QuotexAPI(SESSION_ID);

    // معالجات الأحداث للبيانات الواردة
    quotex.onInstrumentsList = (data) => {
        console.log(\'Received Instruments List:\', data);
    };

    quotex.onMarketData = (data) => {
        console.log(\'Received Market Data (Time Sync):\', data);
    };

    quotex.onHistoricalCandles = (data) => {
        console.log(\'Received Historical Candles:\', data);
    };

    quotex.onAccountBalance = (data) => {
        console.log(\'Received Account Balance:\', data);
    };

    quotex.onProfitPercentage = (data) => {
        console.log(\'Received Profit Percentage:\', data);
    };

    quotex.onTradeHistory = (data) => {
        console.log(\'Received Trade History:\', data);
    };

    quotex.onTradeResult = (data) => {
        console.log(\'Trade Result:\', data);
    };

    // معالجات الأحداث العامة
    quotex.on(\'authenticated\', () => {
        console.log(\'API Authenticated!\');
    });

    quotex.on(\'disconnected\', (code, reason) => {
        console.log(`API Disconnected: Code ${code}, Reason: ${reason}`);
    });

    quotex.on(\'error\', (error) => {
        console.error(\'API Error:\', error.message);
    });

    try {
        await quotex.connect();
        console.log(\'Connected and authenticated to Quotex.\');

        // طلب البيانات باستخدام async/await
        const instruments = await quotex.getInstrumentsList();
        console.log(\'Instruments fetched via async/await:\', instruments);

        // الاشتراك في بيانات السوق (يتم التعامل معها بواسطة onMarketData callback)
        quotex.getMarketData(36); // مثال: الاشتراك في بيانات سوق AUDCAD

        const historicalCandles = await quotex.getHistoricalCandles(36, 60, 100); // مثال: طلب 100 شمعة لـ AUDCAD بإطار زمني 60 ثانية
        console.log(\'Historical Candles fetched via async/await:\', historicalCandles);

        const accountBalance = await quotex.getAccountBalance();
        console.log(\'Account Balance fetched via async/await:\', accountBalance);

        const profitPercentage = await quotex.getProfitPercentage(36); // مثال: طلب نسبة الربح لـ AUDCAD
        console.log(\'Profit Percentage fetched via async/await:\', profitPercentage);

        const tradeHistory = await quotex.getTradeHistory();
        console.log(\'Trade History fetched via async/await:\', tradeHistory);

        // مثال على وضع صفقة (قم بإلغاء التعليق للاختبار)
        // const tradeResult = await quotex.placeTrade(36, 10, \'call\', 60); // مثال: معرف الأداة 36، المبلغ 10، \'call\' (شراء)، 60 ثانية
        // console.log(\'Trade Result via async/await:\', tradeResult);

    } catch (error) {
        console.error(\'Failed to connect or perform operations:\', error);
    }
}

main();
```

### جلب قائمة الأدوات المالية

```javascript
const instruments = await quotex.getInstrumentsList();
console.log(\'Instruments fetched via async/await:\', instruments);
```

### جلب بيانات السوق (الأسعار اللحظية)

```javascript
// instrumentId هو معرف الأداة المالية (مثال: 36 لـ AUDCAD)
// يتم التعامل مع البيانات الواردة عبر quotex.onMarketData
quotex.getMarketData(36);
```

### جلب البيانات التاريخية للشموع

```javascript
// instrumentId: معرف الأداة المالية
// timeframe: الإطار الزمني بالثواني (مثال: 60 لشموع الدقيقة)
// count: عدد الشموع المطلوبة
const historicalCandles = await quotex.getHistoricalCandles(36, 60, 100);
console.log(\'Historical Candles fetched via async/await:\', historicalCandles);
```

### جلب الرصيد

```javascript
const accountBalance = await quotex.getAccountBalance();
console.log(\'Account Balance fetched via async/await:\', accountBalance);
```

### جلب نسبة الربح لكل زوج

```javascript
// instrumentId هو معرف الأداة المالية
const profitPercentage = await quotex.getProfitPercentage(36);
console.log(\'Profit Percentage fetched via async/await:\', profitPercentage);
```

### جلب سجل الصفقات (المغلقة والمفتوحة)

```javascript
const tradeHistory = await quotex.getTradeHistory();
console.log(\'Trade History fetched via async/await:\', tradeHistory);
```

### إجراء عملية تداول

```javascript
// instrumentId: معرف الأداة المالية
// amount: مبلغ التداول
// direction: اتجاه التداول (\'call\' للشراء، \'put\' للبيع)
// duration: مدة الصفقة بالثواني
const tradeResult = await quotex.placeTrade(36, 10, \'call\', 60);
console.log(\'Trade Result via async/await:\', tradeResult);
```

## المساهمة

نرحب بالمساهمات لتحسين هذه المكتبة. يرجى فتح مشكلة (issue) أو طلب سحب (pull request) على مستودع GitHub.

## الترخيص

هذه المكتبة مرخصة تحت ترخيص ISC.


