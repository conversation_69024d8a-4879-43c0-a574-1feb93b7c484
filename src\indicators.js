/**
 * مكتبة المؤشرات الفنية للتحليل الفني المتقدم
 * Technical Indicators Library for Advanced Analysis
 */

class TechnicalIndicators {
    constructor() {
        this.name = 'TechnicalIndicators';
    }

    /**
     * حساب المتوسط المتحرك البسيط (SMA)
     * Simple Moving Average
     */
    static sma(data, period) {
        if (!data || data.length < period) return [];
        
        const result = [];
        for (let i = period - 1; i < data.length; i++) {
            const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
            result.push(sum / period);
        }
        return result;
    }

    /**
     * حساب المتوسط المتحرك الأسي (EMA)
     * Exponential Moving Average
     */
    static ema(data, period) {
        if (!data || data.length < period) return [];
        
        const multiplier = 2 / (period + 1);
        const result = [];
        
        // البداية بـ SMA للقيمة الأولى
        const smaFirst = data.slice(0, period).reduce((a, b) => a + b, 0) / period;
        result.push(smaFirst);
        
        for (let i = period; i < data.length; i++) {
            const ema = (data[i] * multiplier) + (result[result.length - 1] * (1 - multiplier));
            result.push(ema);
        }
        
        return result;
    }

    /**
     * حساب مؤشر القوة النسبية (RSI)
     * Relative Strength Index
     */
    static rsi(data, period = 14) {
        if (!data || data.length < period + 1) return [];
        
        const gains = [];
        const losses = [];
        
        // حساب المكاسب والخسائر
        for (let i = 1; i < data.length; i++) {
            const change = data[i] - data[i - 1];
            gains.push(change > 0 ? change : 0);
            losses.push(change < 0 ? Math.abs(change) : 0);
        }
        
        const result = [];
        
        // حساب RSI
        for (let i = period - 1; i < gains.length; i++) {
            const avgGain = gains.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;
            const avgLoss = losses.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;
            
            if (avgLoss === 0) {
                result.push(100);
            } else {
                const rs = avgGain / avgLoss;
                const rsi = 100 - (100 / (1 + rs));
                result.push(rsi);
            }
        }
        
        return result;
    }

    /**
     * حساب MACD
     * Moving Average Convergence Divergence
     */
    static macd(data, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {
        if (!data || data.length < slowPeriod) return { macd: [], signal: [], histogram: [] };
        
        const fastEMA = this.ema(data, fastPeriod);
        const slowEMA = this.ema(data, slowPeriod);
        
        // حساب MACD Line
        const macdLine = [];
        const startIndex = slowPeriod - fastPeriod;
        
        for (let i = 0; i < slowEMA.length; i++) {
            macdLine.push(fastEMA[i + startIndex] - slowEMA[i]);
        }
        
        // حساب Signal Line
        const signalLine = this.ema(macdLine, signalPeriod);
        
        // حساب Histogram
        const histogram = [];
        const signalStartIndex = macdLine.length - signalLine.length;
        
        for (let i = 0; i < signalLine.length; i++) {
            histogram.push(macdLine[i + signalStartIndex] - signalLine[i]);
        }
        
        return {
            macd: macdLine,
            signal: signalLine,
            histogram: histogram
        };
    }

    /**
     * حساب نطاقات بولينجر (Bollinger Bands)
     */
    static bollingerBands(data, period = 20, stdDev = 2) {
        if (!data || data.length < period) return { upper: [], middle: [], lower: [] };
        
        const sma = this.sma(data, period);
        const upper = [];
        const lower = [];
        
        for (let i = 0; i < sma.length; i++) {
            const dataSlice = data.slice(i, i + period);
            const mean = sma[i];
            const variance = dataSlice.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / period;
            const standardDeviation = Math.sqrt(variance);
            
            upper.push(mean + (standardDeviation * stdDev));
            lower.push(mean - (standardDeviation * stdDev));
        }
        
        return {
            upper: upper,
            middle: sma,
            lower: lower
        };
    }

    /**
     * حساب ATR (Average True Range)
     */
    static atr(high, low, close, period = 14) {
        if (!high || !low || !close || high.length < period) return [];
        
        const trueRanges = [];
        
        for (let i = 1; i < high.length; i++) {
            const tr1 = high[i] - low[i];
            const tr2 = Math.abs(high[i] - close[i - 1]);
            const tr3 = Math.abs(low[i] - close[i - 1]);
            
            trueRanges.push(Math.max(tr1, tr2, tr3));
        }
        
        return this.sma(trueRanges, period);
    }

    /**
     * حساب Stochastic Oscillator
     */
    static stochastic(high, low, close, kPeriod = 14, dPeriod = 3) {
        if (!high || !low || !close || high.length < kPeriod) return { k: [], d: [] };
        
        const kValues = [];
        
        for (let i = kPeriod - 1; i < high.length; i++) {
            const highestHigh = Math.max(...high.slice(i - kPeriod + 1, i + 1));
            const lowestLow = Math.min(...low.slice(i - kPeriod + 1, i + 1));
            
            const k = ((close[i] - lowestLow) / (highestHigh - lowestLow)) * 100;
            kValues.push(k);
        }
        
        const dValues = this.sma(kValues, dPeriod);
        
        return {
            k: kValues,
            d: dValues
        };
    }

    /**
     * كشف أنماط الشموع
     * Candlestick Pattern Detection
     */
    static detectCandlestickPatterns(ohlc) {
        const patterns = [];
        
        for (let i = 1; i < ohlc.length; i++) {
            const current = ohlc[i];
            const previous = ohlc[i - 1];
            
            // Doji Pattern
            if (this.isDoji(current)) {
                patterns.push({ index: i, pattern: 'doji', strength: 'medium' });
            }
            
            // Hammer Pattern
            if (this.isHammer(current)) {
                patterns.push({ index: i, pattern: 'hammer', strength: 'strong' });
            }
            
            // Engulfing Pattern
            if (this.isEngulfing(previous, current)) {
                const type = current.close > current.open ? 'bullish_engulfing' : 'bearish_engulfing';
                patterns.push({ index: i, pattern: type, strength: 'very_strong' });
            }
        }
        
        return patterns;
    }

    // مساعدات لكشف الأنماط
    static isDoji(candle) {
        const bodySize = Math.abs(candle.close - candle.open);
        const totalRange = candle.high - candle.low;
        return bodySize / totalRange < 0.1;
    }

    static isHammer(candle) {
        const bodySize = Math.abs(candle.close - candle.open);
        const lowerShadow = Math.min(candle.open, candle.close) - candle.low;
        const upperShadow = candle.high - Math.max(candle.open, candle.close);
        
        return lowerShadow > bodySize * 2 && upperShadow < bodySize * 0.5;
    }

    static isEngulfing(prev, current) {
        const prevBullish = prev.close > prev.open;
        const currentBullish = current.close > current.open;
        
        return prevBullish !== currentBullish &&
               current.open < Math.min(prev.open, prev.close) &&
               current.close > Math.max(prev.open, prev.close);
    }

    /**
     * حساب مستويات الدعم والمقاومة
     */
    static supportResistance(data, window = 20) {
        const supports = [];
        const resistances = [];
        
        for (let i = window; i < data.length - window; i++) {
            const slice = data.slice(i - window, i + window + 1);
            const current = data[i];
            
            const isSupport = slice.every(price => price >= current);
            const isResistance = slice.every(price => price <= current);
            
            if (isSupport) supports.push({ index: i, level: current });
            if (isResistance) resistances.push({ index: i, level: current });
        }
        
        return { supports, resistances };
    }
}

module.exports = TechnicalIndicators;
