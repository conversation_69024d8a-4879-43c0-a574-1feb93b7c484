/**
 * نظام التداول الذكي المتقدم
 * Advanced Smart Trading System
 */

const TechnicalIndicators = require('./indicators');
const RiskManagement = require('./riskManagement');

class SmartTrading {
    constructor(quotexAPI, options = {}) {
        this.api = quotexAPI;
        this.riskManager = new RiskManagement(options.riskSettings);
        
        // إعدادات التداول
        this.settings = {
            autoTrade: options.autoTrade || false,
            minConfidence: options.minConfidence || 0.7,
            maxTradesPerDay: options.maxTradesPerDay || 10,
            tradingHours: options.tradingHours || { start: 8, end: 18 },
            enabledStrategies: options.enabledStrategies || ['rsi', 'macd', 'bollinger'],
            ...options
        };

        // بيانات السوق
        this.marketData = new Map();
        this.signals = [];
        this.activeStrategies = new Map();
        
        // إحصائيات
        this.todayTrades = 0;
        this.lastTradeTime = null;
        
        this.initializeStrategies();
    }

    /**
     * تهيئة الاستراتيجيات
     */
    initializeStrategies() {
        // استراتيجية RSI
        this.activeStrategies.set('rsi', {
            name: 'RSI Strategy',
            enabled: this.settings.enabledStrategies.includes('rsi'),
            params: { period: 14, oversold: 30, overbought: 70 },
            analyze: this.analyzeRSI.bind(this)
        });

        // استراتيجية MACD
        this.activeStrategies.set('macd', {
            name: 'MACD Strategy',
            enabled: this.settings.enabledStrategies.includes('macd'),
            params: { fast: 12, slow: 26, signal: 9 },
            analyze: this.analyzeMACD.bind(this)
        });

        // استراتيجية Bollinger Bands
        this.activeStrategies.set('bollinger', {
            name: 'Bollinger Bands Strategy',
            enabled: this.settings.enabledStrategies.includes('bollinger'),
            params: { period: 20, stdDev: 2 },
            analyze: this.analyzeBollingerBands.bind(this)
        });

        // استراتيجية أنماط الشموع
        this.activeStrategies.set('candlestick', {
            name: 'Candlestick Patterns',
            enabled: this.settings.enabledStrategies.includes('candlestick'),
            params: { minPatternStrength: 'medium' },
            analyze: this.analyzeCandlestickPatterns.bind(this)
        });
    }

    /**
     * تحليل البيانات وإنتاج الإشارات
     */
    async analyzeMarket(instrumentId, candleData) {
        if (!candleData || candleData.length < 50) {
            console.log('⚠️ Insufficient data for analysis');
            return null;
        }

        // تحديث بيانات السوق
        this.marketData.set(instrumentId, candleData);

        const signals = [];
        
        // تشغيل جميع الاستراتيجيات المفعلة
        for (const [strategyName, strategy] of this.activeStrategies) {
            if (strategy.enabled) {
                try {
                    const signal = await strategy.analyze(instrumentId, candleData);
                    if (signal) {
                        signals.push({
                            strategy: strategyName,
                            ...signal
                        });
                    }
                } catch (error) {
                    console.error(`❌ Error in ${strategyName} strategy:`, error);
                }
            }
        }

        // دمج الإشارات وحساب الثقة الإجمالية
        const combinedSignal = this.combineSignals(signals, instrumentId);
        
        if (combinedSignal && combinedSignal.confidence >= this.settings.minConfidence) {
            return combinedSignal;
        }

        return null;
    }

    /**
     * تحليل RSI
     */
    analyzeRSI(instrumentId, candleData) {
        const closes = candleData.map(c => c.close);
        const rsi = TechnicalIndicators.rsi(closes, 14);
        
        if (rsi.length === 0) return null;
        
        const currentRSI = rsi[rsi.length - 1];
        const prevRSI = rsi[rsi.length - 2];
        
        let signal = null;
        let confidence = 0;

        if (currentRSI < 30 && prevRSI >= 30) {
            // إشارة شراء - RSI خرج من منطقة التشبع البيعي
            signal = 'call';
            confidence = 0.7;
        } else if (currentRSI > 70 && prevRSI <= 70) {
            // إشارة بيع - RSI دخل منطقة التشبع الشرائي
            signal = 'put';
            confidence = 0.7;
        }

        if (signal) {
            return {
                direction: signal,
                confidence: confidence,
                reason: `RSI ${signal === 'call' ? 'oversold' : 'overbought'} signal`,
                indicators: { rsi: currentRSI }
            };
        }

        return null;
    }

    /**
     * تحليل MACD
     */
    analyzeMACD(instrumentId, candleData) {
        const closes = candleData.map(c => c.close);
        const macd = TechnicalIndicators.macd(closes, 12, 26, 9);
        
        if (macd.macd.length < 2 || macd.signal.length < 2) return null;
        
        const currentMACD = macd.macd[macd.macd.length - 1];
        const currentSignal = macd.signal[macd.signal.length - 1];
        const prevMACD = macd.macd[macd.macd.length - 2];
        const prevSignal = macd.signal[macd.signal.length - 2];
        
        let signal = null;
        let confidence = 0;

        // تقاطع صاعد
        if (currentMACD > currentSignal && prevMACD <= prevSignal) {
            signal = 'call';
            confidence = 0.75;
        }
        // تقاطع هابط
        else if (currentMACD < currentSignal && prevMACD >= prevSignal) {
            signal = 'put';
            confidence = 0.75;
        }

        if (signal) {
            return {
                direction: signal,
                confidence: confidence,
                reason: `MACD ${signal === 'call' ? 'bullish' : 'bearish'} crossover`,
                indicators: { 
                    macd: currentMACD, 
                    signal: currentSignal,
                    histogram: macd.histogram[macd.histogram.length - 1]
                }
            };
        }

        return null;
    }

    /**
     * تحليل Bollinger Bands
     */
    analyzeBollingerBands(instrumentId, candleData) {
        const closes = candleData.map(c => c.close);
        const bb = TechnicalIndicators.bollingerBands(closes, 20, 2);
        
        if (bb.upper.length === 0) return null;
        
        const currentPrice = closes[closes.length - 1];
        const upperBand = bb.upper[bb.upper.length - 1];
        const lowerBand = bb.lower[bb.lower.length - 1];
        const middleBand = bb.middle[bb.middle.length - 1];
        
        let signal = null;
        let confidence = 0;

        // السعر يلامس النطاق السفلي
        if (currentPrice <= lowerBand) {
            signal = 'call';
            confidence = 0.65;
        }
        // السعر يلامس النطاق العلوي
        else if (currentPrice >= upperBand) {
            signal = 'put';
            confidence = 0.65;
        }

        if (signal) {
            return {
                direction: signal,
                confidence: confidence,
                reason: `Price touched ${signal === 'call' ? 'lower' : 'upper'} Bollinger Band`,
                indicators: { 
                    price: currentPrice,
                    upperBand: upperBand,
                    lowerBand: lowerBand,
                    middleBand: middleBand
                }
            };
        }

        return null;
    }

    /**
     * تحليل أنماط الشموع
     */
    analyzeCandlestickPatterns(instrumentId, candleData) {
        if (candleData.length < 2) return null;
        
        const patterns = TechnicalIndicators.detectCandlestickPatterns(candleData.slice(-10));
        
        if (patterns.length === 0) return null;
        
        const latestPattern = patterns[patterns.length - 1];
        let signal = null;
        let confidence = 0;

        switch (latestPattern.pattern) {
            case 'hammer':
                signal = 'call';
                confidence = 0.6;
                break;
            case 'bullish_engulfing':
                signal = 'call';
                confidence = 0.8;
                break;
            case 'bearish_engulfing':
                signal = 'put';
                confidence = 0.8;
                break;
            case 'doji':
                // Doji يشير إلى تردد - نحتاج سياق إضافي
                confidence = 0.3;
                break;
        }

        if (signal && confidence >= 0.5) {
            return {
                direction: signal,
                confidence: confidence,
                reason: `${latestPattern.pattern} pattern detected`,
                indicators: { 
                    pattern: latestPattern.pattern,
                    strength: latestPattern.strength
                }
            };
        }

        return null;
    }

    /**
     * دمج الإشارات من استراتيجيات متعددة
     */
    combineSignals(signals, instrumentId) {
        if (signals.length === 0) return null;

        // تجميع الإشارات حسب الاتجاه
        const callSignals = signals.filter(s => s.direction === 'call');
        const putSignals = signals.filter(s => s.direction === 'put');

        let finalDirection = null;
        let totalConfidence = 0;
        let reasons = [];

        if (callSignals.length > putSignals.length) {
            finalDirection = 'call';
            totalConfidence = callSignals.reduce((sum, s) => sum + s.confidence, 0) / callSignals.length;
            reasons = callSignals.map(s => s.reason);
        } else if (putSignals.length > callSignals.length) {
            finalDirection = 'put';
            totalConfidence = putSignals.reduce((sum, s) => sum + s.confidence, 0) / putSignals.length;
            reasons = putSignals.map(s => s.reason);
        } else {
            // تعادل في الإشارات - نختار الأعلى ثقة
            const bestCall = callSignals.reduce((best, current) => 
                current.confidence > best.confidence ? current : best, { confidence: 0 });
            const bestPut = putSignals.reduce((best, current) => 
                current.confidence > best.confidence ? current : best, { confidence: 0 });

            if (bestCall.confidence > bestPut.confidence) {
                finalDirection = 'call';
                totalConfidence = bestCall.confidence;
                reasons = [bestCall.reason];
            } else {
                finalDirection = 'put';
                totalConfidence = bestPut.confidence;
                reasons = [bestPut.reason];
            }
        }

        return {
            instrumentId: instrumentId,
            direction: finalDirection,
            confidence: totalConfidence,
            reasons: reasons,
            signalCount: signals.length,
            timestamp: new Date(),
            allSignals: signals
        };
    }

    /**
     * تنفيذ صفقة ذكية
     */
    async executeTrade(signal, accountBalance, amount = null) {
        // التحقق من ساعات التداول
        if (!this.isTradingHours()) {
            return { success: false, reason: 'Outside trading hours' };
        }

        // التحقق من عدد الصفقات اليومية
        if (this.todayTrades >= this.settings.maxTradesPerDay) {
            return { success: false, reason: 'Daily trade limit reached' };
        }

        // فحص إدارة المخاطر
        const riskCheck = this.riskManager.canOpenTrade(accountBalance, amount || 10);
        if (!riskCheck.allowed) {
            return { success: false, reason: 'Risk management check failed', details: riskCheck };
        }

        const tradeAmount = amount || riskCheck.recommendedAmount;

        try {
            // محاكاة تنفيذ الصفقة (للاختبار)
            const result = {
                success: true,
                tradeId: Date.now(),
                instrumentId: signal.instrumentId,
                direction: signal.direction,
                amount: tradeAmount,
                timestamp: new Date()
            };

            // تسجيل الصفقة
            this.todayTrades++;
            this.lastTradeTime = new Date();

            console.log('✅ Trade executed successfully:', {
                instrumentId: signal.instrumentId,
                direction: signal.direction,
                amount: tradeAmount,
                confidence: signal.confidence,
                reasons: signal.reasons
            });

            return {
                success: true,
                result: result,
                signal: signal,
                amount: tradeAmount,
                riskCheck: riskCheck
            };

        } catch (error) {
            console.error('❌ Trade execution failed:', error);
            return { success: false, reason: 'Execution error', error: error.message };
        }
    }

    /**
     * التحقق من ساعات التداول
     */
    isTradingHours() {
        const now = new Date();
        const hour = now.getHours();
        return hour >= this.settings.tradingHours.start && hour <= this.settings.tradingHours.end;
    }

    /**
     * الحصول على تقرير الأداء
     */
    getPerformanceReport() {
        return {
            todayTrades: this.todayTrades,
            lastTradeTime: this.lastTradeTime,
            activeStrategies: Array.from(this.activeStrategies.entries()).map(([name, strategy]) => ({
                name: strategy.name,
                enabled: strategy.enabled,
                params: strategy.params
            })),
            riskManagement: this.riskManager.getFullReport(),
            settings: this.settings
        };
    }
}

module.exports = SmartTrading;
